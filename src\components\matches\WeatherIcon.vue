<script setup lang="ts">
import { computed } from 'vue'
import {
  Sun,
  CloudSun,
  Cloud,
  CloudFog,
  CloudDrizzle,
  CloudRain,
  CloudSnow,
  CloudRainWind,
  CloudLightning,
} from 'lucide-vue-next'
import type { FunctionalComponent } from 'vue'
import type { LucideProps } from 'lucide-vue-next'

// Map Open-Meteo weather codes to Lucide icon components
const weatherCodeToIcon: Record<number, FunctionalComponent<LucideProps>> = {
  0: Sun,                // Clear sky
  1: CloudSun,           // Mainly clear
  2: Cloud,              // Partly cloudy
  3: Cloud,              // Overcast
  45: CloudFog,          // Fog
  48: CloudFog,          // Depositing rime fog
  51: CloudDrizzle,      // Drizzle: Light
  53: CloudDrizzle,      // Drizzle: Moderate
  55: CloudDrizzle,      // Drizzle: Dense
  56: CloudSnow,         // Freezing Drizzle: Light
  57: CloudSnow,         // Freezing Drizzle: Dense
  61: CloudRain,         // Rain: Slight
  63: CloudRain,         // Rain: Moderate
  65: CloudRain,         // Rain: Heavy
  66: CloudSnow,         // Freezing Rain: Light
  67: CloudSnow,         // Freezing Rain: Heavy
  71: CloudSnow,         // Snow fall: Slight
  73: CloudSnow,         // Snow fall: Moderate
  75: CloudSnow,         // Snow fall: Heavy
  77: CloudSnow,         // Snow grains
  80: CloudRainWind,     // Rain showers: Slight
  81: CloudRainWind,     // Rain showers: Moderate
  82: CloudRainWind,     // Rain showers: Violent
  85: CloudSnow,         // Snow showers: Slight
  86: CloudSnow,         // Snow showers: Heavy
  95: CloudLightning,    // Thunderstorm: Slight/Moderate
  96: CloudLightning,    // Thunderstorm with hail: Slight
  99: CloudLightning,    // Thunderstorm with hail: Heavy
}

const props = defineProps({
  weatherCode: {
    type: Number,
    required: true,
  },
  size: {
    type: [Number, String],
    default: 48,
  },
  color: {
    type: String,
    default: 'currentColor',
  },
})

// Pick icon or fallback to Sun
const IconComponent = computed(() => weatherCodeToIcon[props.weatherCode] || Sun)
</script>

<template>
  <component :is="IconComponent" :size="size" :color="color" />
</template>