import type { Match as FeathersMatch, MatchData as FeathersMatchData, Player as FeathersPlayer, Equipment } from '@/api/feathers-client';
import type { AgeDivision, StyleDivision } from '@/stores/federations';
import type { AgendaEntry } from '@/stores/matches';

declare module '@/api/feathers-client' {
  interface Match extends FeathersMatch {
    isActive?: boolean;
    international?: boolean;
    withoutLimits?: boolean;
    licenseRequired?: boolean;
    judges?: string[];
    agenda?: AgendaEntry[];
    payments?: Record<string, number>;
    ageDivisions?: AgeDivision[] | string[];
    styleDivisions?: StyleDivision[] | string[];
  }

  interface MatchData extends FeathersMatchData {
    isActive?: boolean;
    international?: boolean;
    withoutLimits?: boolean;
    licenseRequired?: boolean;
    judges?: string[];
    agenda?: AgendaEntry[];
    payments?: Record<string, number>;
    ageDivisions?: AgeDivision[] | string[];
    styleDivisions?: StyleDivision[] | string[];
  }

  interface Player extends FeathersPlayer {
    equipment?: Equipment[];
  }
}
