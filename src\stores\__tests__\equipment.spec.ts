import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useEquipmentStore } from '../equipment'
import { useAuthStore } from '../auth'
import { useUserStore } from '../user'

// Mock the API services
vi.mock('../../api/feathers-client', () => ({
  api: {
    equipment: {
      find: vi.fn(),
      create: vi.fn(),
      patch: vi.fn(),
      remove: vi.fn()
    }
  }
}))

// Mock the user store
vi.mock('../user', () => ({
  useUserStore: vi.fn(() => ({
    activePlayer: {
      id: 1,
      equipment: []
    },
    fetchUserProfile: vi.fn()
  }))
}))

// Mock the auth store
vi.mock('../auth', () => ({
  useAuthStore: vi.fn(() => ({
    isAuthenticated: true
  }))
}))

describe('Equipment Store', () => {
  let equipmentStore: ReturnType<typeof useEquipmentStore>
  let userStore: ReturnType<typeof useUserStore>
  let authStore: ReturnType<typeof useAuthStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    equipmentStore = useEquipmentStore()
    userStore = useUserStore()
    authStore = useAuthStore()

    // Reset activePlayer.equipment before each test
    userStore.activePlayer.equipment = []
  })

  it('should initialize with empty state', () => {
    expect(equipmentStore.isLoading).toBe(false)
    expect(equipmentStore.error).toBeNull()
  })

  it('should create equipment and refresh user profile', async () => {
    const mockEquipmentData = {
      name: 'New Bow',
      category: 'Recurve',
      playerId: 1
    }
    const createdEquipment = { id: 1, ...mockEquipmentData }

    // Mock API response
    vi.mocked(equipmentStore.equipmentService.create).mockResolvedValue(createdEquipment)

    // Call the action
    const result = await equipmentStore.createEquipment(mockEquipmentData)

    // Assertions
    expect(equipmentStore.equipmentService.create).toHaveBeenCalledWith(mockEquipmentData)
    expect(userStore.fetchUserProfile).toHaveBeenCalled()
    expect(result).toEqual(createdEquipment)
    expect(equipmentStore.isLoading).toBe(false)
    expect(equipmentStore.error).toBeNull()
  })

  it('should update equipment and refresh user profile', async () => {
    const equipmentId = 1
    const updateData = { name: 'Updated Bow' }
    const updatedEquipment = { id: equipmentId, name: 'Updated Bow', category: 'Recurve' }

    // Mock API response
    vi.mocked(equipmentStore.equipmentService.patch).mockResolvedValue(updatedEquipment)

    // Call the action
    const result = await equipmentStore.updateEquipment(equipmentId, updateData)

    // Assertions
    expect(equipmentStore.equipmentService.patch).toHaveBeenCalledWith(equipmentId, updateData)
    expect(userStore.fetchUserProfile).toHaveBeenCalled()
    expect(result).toEqual(updatedEquipment)
    expect(equipmentStore.isLoading).toBe(false)
    expect(equipmentStore.error).toBeNull()
  })

  it('should delete equipment and refresh user profile', async () => {
    const equipmentId = 1

    // Mock API response
    vi.mocked(equipmentStore.equipmentService.remove).mockResolvedValue({ id: equipmentId })

    // Call the action
    await equipmentStore.deleteEquipment(equipmentId)

    // Assertions
    expect(equipmentStore.equipmentService.remove).toHaveBeenCalledWith(equipmentId)
    expect(userStore.fetchUserProfile).toHaveBeenCalled()
    expect(equipmentStore.isLoading).toBe(false)
    expect(equipmentStore.error).toBeNull()
  })

  it('should handle errors during create equipment', async () => {
    const mockError = new Error('Create failed')
    vi.mocked(equipmentStore.equipmentService.create).mockRejectedValue(mockError)

    await expect(equipmentStore.createEquipment({ name: 'Fail Bow', category: 'Recurve', playerId: 1 })).rejects.toThrow(mockError)
    expect(equipmentStore.isLoading).toBe(false)
    expect(equipmentStore.error).toEqual(mockError)
    expect(userStore.fetchUserProfile).not.toHaveBeenCalled()
  })

  it('should not create equipment if user is not authenticated', async () => {
    vi.mocked(authStore).isAuthenticated = false
    const mockEquipmentData = { name: 'New Bow', category: 'Recurve', playerId: 1 }

    await equipmentStore.createEquipment(mockEquipmentData)

    expect(equipmentStore.equipmentService.create).not.toHaveBeenCalled()
    expect(equipmentStore.error).toBeInstanceOf(Error)
    expect(equipmentStore.error?.message).toBe('User not authenticated')
  })
})