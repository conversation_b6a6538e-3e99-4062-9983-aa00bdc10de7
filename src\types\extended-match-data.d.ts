import type { AgeDivision, StyleDivision } from '@/stores/federations';
import type { AgendaEntry } from '@/stores/matches';

declare module 'ap-api-feathers' {
  interface MatchData {
    isActive?: boolean;
    international?: boolean;
    withoutLimits?: boolean;
    licenseRequired?: boolean;
    judges?: string[];
    agenda?: AgendaEntry[];
    payments?: Record<string, number>;
    ageDivisions?: AgeDivision[] | string[];
    styleDivisions?: StyleDivision[] | string[];
  }
}