<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Badge } from '@/components/ui/badge'
import MatchSignupWidget from './MatchSignupWidget.vue'
import type { ExtendedMatch } from '@/types/match'

defineProps<{
  items: ExtendedMatch[]
}>()

const router = useRouter()

const goToMatch = (id: number) => {
  router.push({ name: 'match-details', params: { id: id.toString() } })
}

const displayDate = (date?: string) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleDateString('pl-PL', { year: 'numeric', month: 'short', day: 'numeric' })
}
</script>

<template>
  <div class="flex flex-col gap-1">
    <div v-if="!items || items.length === 0" class="p-4 text-center text-muted-foreground text-sm">
      No matches found
    </div>
    <div v-else>
      <div
        v-for="item in items"
        :key="item.id"
        class="w-full flex items-center justify-between px-3 py-2 rounded hover:bg-accent transition text-left text-sm border-b last:border-b-0"
        style="min-height: 0"
      >
        <div class="flex-grow flex flex-col gap-0.5 cursor-pointer" @click="goToMatch(item.id)">
          <span class="font-medium">{{ item.name }}</span>
          <span class="text-xs text-muted-foreground">
            {{ displayDate(item.startDate) }}
            <span v-if="item.city">· {{ item.city }}</span>
            <span v-if="item.country">· {{ item.country }}</span>
          </span>
        </div>

        <div class="flex items-center gap-4">
          <Badge v-if="item.competitionLevel" variant="secondary" class="ml-2 text-xs">
            {{ item.competitionLevel }}
          </Badge>
          <MatchSignupWidget :match="item" :show-label="false" />
        </div>
      </div>
    </div>
  </div>
</template>