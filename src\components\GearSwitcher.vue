<script setup lang="ts">
/**
 * GearSwitcher component displays and manages user's archery equipment.
 *
 * Features:
 * - Shows current selected equipment
 * - Displays equipment list in dropdown
 * - Allows switching between equipment
 * - Provides option to add new equipment
 * - Integrates with equipment store for state management
 */
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'
import { ChevronsUpDown, Plus, Target, BowArrow } from 'lucide-vue-next'

import { computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAuthStore } from '@/stores/auth'
import type { Equipment } from '@/api/feathers-client'

const userStore = useUserStore()
const authStore = useAuthStore()
const { isMobile } = useSidebar()

// Computed properties for display
const activePlayer = computed(() => userStore.activePlayer)
const currentEquipment = computed(() => {
  if (!activePlayer.value || !activePlayer.value.equipment || !activePlayer.value.activeEquipmentId) return null
  return activePlayer.value.equipment.find((e: Equipment) => e.id === activePlayer.value.activeEquipmentId)
})
const equipmentList = computed(() => activePlayer.value?.equipment || [])

// Equipment display helpers
const getEquipmentIcon = (equipment: Equipment) => {
  // Return appropriate icon based on equipment type/category
  if (equipment?.equipmentClass?.toUpperCase() === 'BOW') return BowArrow
  return Target // Default icon
}

const getEquipmentDisplayName = (equipment: Equipment) => {
  return equipment?.name || 'Unknown Equipment'
}

const getEquipmentCategory = (equipment: Equipment) => {
  return equipment?.category || equipment?.type || 'Equipment'
}

// Actions
function selectEquipment(equipment: Equipment) {
  userStore.setActiveEquipment(equipment)
}

function handleAddEquipment() {
  // TODO: Open equipment creation modal/form
  console.log('Add new equipment')
}
</script>

<template>
  <SidebarMenu v-if="authStore.isAuthenticated">
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground bg-sidebar-primary text-sidebar-primary-foreground"
            :disabled="userStore.isLoading"
          >
            <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <component
                v-if="currentEquipment"
                :is="getEquipmentIcon(currentEquipment)"
                class="size-4"
              />
              <Target v-else class="size-4" />
            </div>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-medium">
                {{ currentEquipment ? getEquipmentDisplayName(currentEquipment) : 'No Equipment' }}
              </span>
              <span class="truncate text-xs">
                {{ currentEquipment ? getEquipmentCategory(currentEquipment) : 'Select equipment' }}
              </span>
            </div>
            <ChevronsUpDown class="ml-auto" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="w-[--reka-dropdown-menu-trigger-width] min-w-56 rounded-lg"
          align="start"
          :side="isMobile ? 'bottom' : 'right'"
          :side-offset="4"
        >
          <DropdownMenuLabel class="text-xs text-muted-foreground">
            My bows
          </DropdownMenuLabel>

          <DropdownMenuItem
            v-for="(equipment, index) in equipmentList"
            :key="equipment.id"
            class="gap-2 p-2"
            @click="selectEquipment(equipment)"
          >
            <div class="flex size-6 items-center justify-center rounded-sm border">
              <component :is="getEquipmentIcon(equipment)" class="size-3.5 shrink-0" />
            </div>
            {{ getEquipmentDisplayName(equipment) }}
            <DropdownMenuShortcut>{{ index + 1 }}</DropdownMenuShortcut>
          </DropdownMenuItem>

          <DropdownMenuSeparator />
          <DropdownMenuItem class="gap-2 p-2" @click="handleAddEquipment">
            <div class="flex size-6 items-center justify-center rounded-md border bg-transparent">
              <Plus class="size-4" />
            </div>
            <div class="font-medium text-muted-foreground">
              Add bow
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  </SidebarMenu>
</template>