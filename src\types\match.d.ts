import type { Match as ApiMatch } from '@/api/feathers-client'

interface ApiAgeCategory {
  id: string | number;
  name?: string;
  short_name?: string;
  abbreviation?: string;
  min_age?: number;
  min?: number;
  max_age?: number;
  max?: number;
}

interface ApiPaymentEntry {
  amount: number;
  currency: string;
}

export interface ExtendedMatchOptionalFields {
  distanceKm?: number;
  sunriseTime?: string;
  sunsetTime?: string;
  temperatureCelsius?: number;
  participantProgress?: number;
  participantsCount?: number;
  competitionLevel?: string;
  international?: boolean;
  matchType?: string;
  competition_level?: string;
  is_international?: boolean;
  match_type?: string;
  participants_count?: number;
  payments?: ApiPaymentEntry[];
  ageCategories?: ApiAgeCategory[];
  equipmentCategories?: any[];
}

export type ExtendedMatch = ApiMatch & ExtendedMatchOptionalFields;
