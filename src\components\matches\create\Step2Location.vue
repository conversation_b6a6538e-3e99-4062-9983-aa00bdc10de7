<script setup lang="ts">
import { watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import LocationPickerMap from '@/components/map/LocationPickerMap.vue'
import { EU_COUNTRIES } from '@/stores/matches'

const props = defineProps<{
  city?: string
  address?: string
  country?: string
  postcode?: string
  latitude?: number
  longitude?: number
}>()

const emit = defineEmits<{
  'update:city': [value: string | undefined]
  'update:address': [value: string | undefined]
  'update:country': [value: string | undefined]
  'update:postcode': [value: string | undefined]
  'update:latitude': [value: number | undefined]
  'update:longitude': [value: number | undefined]
}>()

const { t } = useI18n()

// Debug coordinate changes
watch([() => props.latitude, () => props.longitude], ([newLat, newLng]) => {
  console.log('[Step2Location] Coordinate props changed:', {
    latitude: newLat,
    longitude: newLng,
    types: { lat: typeof newLat, lng: typeof newLng }
  })
})
</script>

<template>
  <div class="space-y-6">
    <div class="flex flex-wrap gap-4 items-end">
      <div class="space-y-2 flex-none">
        <Label for="country">{{ t('matches.country') }}</Label>
        <Select
          :model-value="country"
          @update:model-value="emit('update:country', $event)"
        >
          <SelectTrigger>
            <SelectValue placeholder="Select country" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="countryItem in EU_COUNTRIES"
              :key="countryItem.code"
              :value="countryItem.code"
            >
              {{ countryItem.name }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div class="space-y-2 flex-none w-24">
        <Label for="postcode">{{ t('matches.postcode') }}</Label>
        <Input
          id="postcode"
          :model-value="postcode"
          @update:model-value="emit('update:postcode', $event)"
          :placeholder="t('matches.postcode')"
        />
      </div>
      <div class="space-y-2 flex-none">
        <Label for="city">{{ t('matches.city') }}</Label>
        <Input
          id="city"
          :model-value="city"
          @update:model-value="emit('update:city', $event)"
          :placeholder="t('matches.city')"
        />
      </div>
    </div>
    <div class="grid grid-cols-1 gap-4">
      <div class="space-y-2">
        <Label for="address">{{ t('matches.address') }}</Label>
        <Input
          id="address"
          :model-value="address"
          @update:model-value="emit('update:address', $event)"
          :placeholder="t('matches.address')"
        />
      </div>
    </div>

    <!-- Interactive Map -->
    <div class="space-y-2">
      <Label>Location on Map</Label>
      <LocationPickerMap
        :latitude="latitude"
        :longitude="longitude"
        :address="address"
        :city="city"
        :country="country"
        :postcode="postcode"
        @update:latitude="emit('update:latitude', $event || undefined)"
        @update:longitude="emit('update:longitude', $event || undefined)"
        @update:address="emit('update:address', $event)"
        @update:city="emit('update:city', $event)"
        @update:country="emit('update:country', $event)"
        @update:postcode="emit('update:postcode', $event)"
      />
    </div>

    <!-- Coordinate Fields -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div class="space-y-2">
        <Label for="latitude">{{ t('matches.latitude') }}</Label>
        <Input
          id="latitude"
          :model-value="latitude?.toString() || ''"
          @update:model-value="(value) => {
            console.log('[Step2Location] Latitude input changed:', value)
            const parsed = value && typeof value === 'string' ? parseFloat(value) : undefined
            console.log('[Step2Location] Emitting latitude:', parsed)
            emit('update:latitude', parsed)
          }"
          type="number"
          step="any"
          placeholder="Latitude"
        />
      </div>
      <div class="space-y-2">
        <Label for="longitude">{{ t('matches.longitude') }}</Label>
        <Input
          id="longitude"
          :model-value="longitude?.toString() || ''"
          @update:model-value="(value) => {
            console.log('[Step2Location] Longitude input changed:', value)
            const parsed = value && typeof value === 'string' ? parseFloat(value) : undefined
            console.log('[Step2Location] Emitting longitude:', parsed)
            emit('update:longitude', parsed)
          }"
          type="number"
          step="any"
          placeholder="Longitude"
        />
      </div>
    </div>
  </div>
</template>
