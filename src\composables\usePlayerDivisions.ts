import type { Equipment as ApiEquipment, Federation as ApiFederation, Player as ApiPlayer } from 'ap-api-feathers'
// src/composables/usePlayerDivisions.ts
import { computed } from 'vue'

import type { ExtendedMatch } from '@/types/match'

interface DivisionDisplay {
  type: 'style' | 'age' | 'sex'
  short_name: string
  name: string
  tooltip?: string
}

function getPlayerAge(birthdate?: string): number | null {
  if (!birthdate) {
    return null
  }
  const today = new Date()
  const birth = new Date(birthdate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  return age
}

export function usePlayerDivisions(
  activePlayer: Partial<ApiPlayer> | null | undefined,
  match: Partial<ExtendedMatch> | null | undefined
) {
  // Style Division
  const styleDivision = computed<DivisionDisplay | null>(() => {
    if (!activePlayer || !match) return null
    const equipmentId = (activePlayer as any).activeEquipmentId
    const equipmentList = (activePlayer as any).equipment as ApiEquipment[] | undefined
    const equipment = equipmentList?.find((eq: any) => eq.id === equipmentId)
    const federationId = (match as any).federationId ?? (match as any).federation?.id
    let styleCat: any = null

    if (equipment?.federationStyles && federationId) {
      // federationStyles: [{category, federation_id, federation_name}]
      styleCat = (equipment.federationStyles as any[]).find(
        (fs: any) => fs.federation_id === federationId
      )
    }

    // styleDivisions can be string[] or object[]
    let styleDivObj: any = null
    if (styleCat) {
      if (Array.isArray(match.styleDivisions)) {
        styleDivObj =
          match.styleDivisions.find(
            (div: any) =>
              (typeof div === 'string' && div === styleCat.category) ||
              (typeof div === 'object' && div.short_name === styleCat.category)
          ) || styleCat
      } else {
        styleDivObj = styleCat
      }
    }

    if (styleDivObj) {
      return {
        type: 'style',
        short_name:
          styleDivObj.short_name ||
          styleDivObj.category ||
          (typeof styleDivObj === 'string' ? styleDivObj : ''),
        name: styleDivObj.name || styleDivObj.category || styleDivObj.short_name || '',
        tooltip: styleDivObj.name || styleDivObj.category || ''
      }
    }
    return null
  })

  // Age Division
  const ageDivision = computed<DivisionDisplay | null>(() => {
    if (!activePlayer || !match) return null
    const playerAge = getPlayerAge((activePlayer as any).birthdate)
    let ageDivObj: any = null

    // match.ageDivisions can be string[] or object[]
    if (Array.isArray((match as any).ageDivisions) && playerAge !== null) {
      for (const div of (match as any).ageDivisions) {
        if (typeof div === 'string') {
          // Lookup in federation's ageDivisions
          const federationAgeDiv =
            (match as any).federation?.ageDivisions?.find(
              (cat: any) => cat.short_name === div
            )
          if (federationAgeDiv) {
            const min = federationAgeDiv.min ?? federationAgeDiv.min_age
            const max = federationAgeDiv.max ?? federationAgeDiv.max_age
            if (
              (min !== undefined && playerAge < min) ||
              (max !== undefined && playerAge > max)
            ) {
              continue
            }
            ageDivObj = federationAgeDiv
            break
          }
        } else if (typeof div === 'object') {
          const min = div.min ?? div.min_age
          const max = div.max ?? div.max_age
          if (
            (min !== undefined && playerAge < min) ||
            (max !== undefined && playerAge > max)
          ) {
            continue
          }
          ageDivObj = div
          break
        }
      }
    }
    // fallback to federation's ageDivisions
    if (!ageDivObj && Array.isArray((match as any).federation?.ageDivisions) && playerAge !== null) {
      for (const cat of (match as any).federation.ageDivisions) {
        const min = cat.min ?? cat.min_age
        const max = cat.max ?? cat.max_age
        if (
          (min !== undefined && playerAge < min) ||
          (max !== undefined && playerAge > max)
        ) {
          continue
        }
        ageDivObj = cat
        break
      }
    }

    if (ageDivObj) {
      return {
        type: 'age',
        short_name: ageDivObj.short_name || ageDivObj.name || '',
        name: ageDivObj.name || ageDivObj.short_name || '',
        tooltip:
          ageDivObj.name ||
          (ageDivObj.min !== undefined && ageDivObj.max !== undefined
            ? `Age ${ageDivObj.min}-${ageDivObj.max}`
            : '')
      }
    }
    return null
  })

  // Sex Division
  const sexDivision = computed<DivisionDisplay | null>(() => {
    if (!activePlayer) return null
    const sex = (activePlayer as any).sex
    if (typeof sex === 'string' && sex.trim() !== '') {
      return {
        type: 'sex',
        short_name: sex,
        name: sex === 'M' ? 'Male' : sex === 'F' ? 'Female' : sex,
        tooltip: sex === 'M' ? 'Male' : sex === 'F' ? 'Female' : sex
      }
    }
    return null
  })

  return {
    styleDivision,
    ageDivision,
    sexDivision
  }
}
